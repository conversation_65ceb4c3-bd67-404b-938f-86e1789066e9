<script setup lang="ts">
const { signOut, data } = useAuth();

const userName = computed(() => data.value?.user?.name);
</script>

<template>
    <div class="flex flex-col min-h-screen items-center justify-center relative">
        <div class="h-[4rem] w-full flex items-center justify-between gap-4 px-4">
            <UIcon
                name="i-custom-sensehawk-logo"
                class="h-9 w-9 animate-fade-in"
            />
            <div class="flex items-center gap-2">
                <p>Welcome, {{ userName }}!</p>
                <UButton
                    color="neutral"
                    variant="outline"
                    size="md"
                    class="cursor-pointer"
                    @click="() => signOut()"
                >
                    Sign Out
                </UButton>
                <ThemeToggle />
            </div>
        </div>
        <div class="w-screen h-[calc(100vh-4rem)]">
            <slot />
        </div>
    </div>
</template>
